﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{908DD309-1A13-3141-A66F-106175FA9BC4}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7} = {34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Continuous", "Continuous.vcxproj", "{919EAD2F-4148-313A-9AC6-A15BBAADD17E}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Experimental", "Experimental.vcxproj", "{619355DB-4DA7-3C8E-8F75-4025D609D7F6}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Nightly", "Nightly.vcxproj", "{25D752A6-C646-345A-BBED-86ADAD424F8C}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "NightlyMemoryCheck", "NightlyMemoryCheck.vcxproj", "{600A3BCE-64DE-347C-8875-BCB4257D38DE}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{AF451698-3A15-3124-B7E2-CEF594DD6262}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{9AC81998-4544-3415-A2D6-D5F1F36E393D}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "aider-quality-cpp", "aider-quality-cpp.vcxproj", "{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}"
	ProjectSection(ProjectDependencies) = postProject
		{9AC81998-4544-3415-A2D6-D5F1F36E393D} = {9AC81998-4544-3415-A2D6-D5F1F36E393D}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{908DD309-1A13-3141-A66F-106175FA9BC4}.Debug|x64.ActiveCfg = Debug|x64
		{908DD309-1A13-3141-A66F-106175FA9BC4}.Release|x64.ActiveCfg = Release|x64
		{908DD309-1A13-3141-A66F-106175FA9BC4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{908DD309-1A13-3141-A66F-106175FA9BC4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{919EAD2F-4148-313A-9AC6-A15BBAADD17E}.Debug|x64.ActiveCfg = Debug|x64
		{919EAD2F-4148-313A-9AC6-A15BBAADD17E}.Release|x64.ActiveCfg = Release|x64
		{919EAD2F-4148-313A-9AC6-A15BBAADD17E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{919EAD2F-4148-313A-9AC6-A15BBAADD17E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{619355DB-4DA7-3C8E-8F75-4025D609D7F6}.Debug|x64.ActiveCfg = Debug|x64
		{619355DB-4DA7-3C8E-8F75-4025D609D7F6}.Release|x64.ActiveCfg = Release|x64
		{619355DB-4DA7-3C8E-8F75-4025D609D7F6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{619355DB-4DA7-3C8E-8F75-4025D609D7F6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{25D752A6-C646-345A-BBED-86ADAD424F8C}.Debug|x64.ActiveCfg = Debug|x64
		{25D752A6-C646-345A-BBED-86ADAD424F8C}.Release|x64.ActiveCfg = Release|x64
		{25D752A6-C646-345A-BBED-86ADAD424F8C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{25D752A6-C646-345A-BBED-86ADAD424F8C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{600A3BCE-64DE-347C-8875-BCB4257D38DE}.Debug|x64.ActiveCfg = Debug|x64
		{600A3BCE-64DE-347C-8875-BCB4257D38DE}.Release|x64.ActiveCfg = Release|x64
		{600A3BCE-64DE-347C-8875-BCB4257D38DE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{600A3BCE-64DE-347C-8875-BCB4257D38DE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AF451698-3A15-3124-B7E2-CEF594DD6262}.Debug|x64.ActiveCfg = Debug|x64
		{AF451698-3A15-3124-B7E2-CEF594DD6262}.Release|x64.ActiveCfg = Release|x64
		{AF451698-3A15-3124-B7E2-CEF594DD6262}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AF451698-3A15-3124-B7E2-CEF594DD6262}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.Debug|x64.ActiveCfg = Debug|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.Debug|x64.Build.0 = Debug|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.Release|x64.ActiveCfg = Release|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.Release|x64.Build.0 = Release|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9AC81998-4544-3415-A2D6-D5F1F36E393D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.Debug|x64.ActiveCfg = Debug|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.Debug|x64.Build.0 = Debug|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.Release|x64.ActiveCfg = Release|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.Release|x64.Build.0 = Release|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{34EC08A1-96FE-3EC9-BA9F-5479C273ADD7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B6777351-45F4-34F0-B505-B54F3F4374B8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
