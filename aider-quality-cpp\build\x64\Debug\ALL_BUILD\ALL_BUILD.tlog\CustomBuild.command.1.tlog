^C:\USERS\<USER>\_ITEMS\DEV\SCALE\AIDER-QUALITY-CPP\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp -BC:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp/build --check-stamp-file C:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
