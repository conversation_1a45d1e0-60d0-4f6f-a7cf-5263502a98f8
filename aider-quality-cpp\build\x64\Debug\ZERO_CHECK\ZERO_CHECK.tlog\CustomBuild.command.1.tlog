^C:\USERS\<USER>\_ITEMS\DEV\SCALE\AIDER-QUALITY-CPP\BUILD\CMAKEFILES\69E4FFB9FBD990E7D49512DB4D9B6EB0\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp -BC:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/_Items/Dev/scale/aider-quality-cpp/build/aider-quality-cpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
