#include <iostream>
#include "solution.h"

using namespace std;

int main() {
    int a;

    // Test case 1: Empty matrix
    a = solution::solve(0, 0, {});
    cout << a << " 0" << endl;

    // Test case 2: 1x1 matrix with false
    a = solution::solve(1, 1, {{false}});
    cout << a << " 0" << endl;

    // Test case 3: 1x1 matrix with true
    a = solution::solve(1, 1, {{true}});
    cout << a << " 1" << endl;

    // Test case 4: 2x2 matrix
    a = solution::solve(2, 2, {
        {false, true},
        {false, true}
    });
    cout << a << " 1" << endl;

    // Test case 5: 3x2 matrix
    a = solution::solve(3, 2, {
        {false, true},
        {true, true},
        {false, true}
    });
    cout << a << " 2" << endl;

    // Test case 6: 5x2 matrix
    a = solution::solve(5, 2, {
        {true, false},
        {false, true},
        {true, false},
        {false, true},
        {true, false}
    });
    cout << a << " 3" << endl;

    // Test case 7: 5x4 matrix
    a = solution::solve(5, 4, {
        {true, false, true, false},
        {false, true, false, false},
        {true, false, true, true},
        {false, true, false, false},
        {true, false, true, false}
    });
    cout << a << " 4" << endl;

    // Test case 8: 8x5 matrix
    a = solution::solve(8, 5, {
        {false, true, true, true, true},
        {false, true, true, true, true},
        {false, true, true, true, true},
        {false, true, true, true, true},
        {false, true, true, true, true},
        {false, true, false, true, true},
        {false, true, false, true, true},
        {false, false, true, true, true}
    });
    cout << a << " 3" << endl;

    // Test case 9: 6x6 matrix
    a = solution::solve(6, 6, {
        {true, false, false, false, false, true},
        {false, true, true, true, true, false},
        {false, true, false, false, true, false},
        {false, true, false, false, true, false},
        {false, true, true, true, true, true},
        {true, false, false, false, false, true}
    });
    cout << a << " 5" << endl;

    // Test case 10: 6x6 matrix
    a = solution::solve(6, 6, {
        {true, false, false, false, false, true},
        {false, false, false, false, true, false},
        {false, false, false, true, false, false},
        {false, false, true, false, false, false},
        {false, true, false, false, false, false},
        {true, false, false, false, false, true}
    });
    cout << a << " 8" << endl;

    return 0;
}
