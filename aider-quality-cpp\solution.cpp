#include "solution.h"
#include <vector>
#include <climits>
using namespace std;

namespace solution {
    int true_count = 0;
    int cur_min = INT_MAX;

    bool flip(vector<vector<bool>> &matrix, int y0, int x0, int h0, int w0) {
        bool had_true = false;

        for (int y = y0; y < y0 + h0; y++) {
            for (int x = x0; x < x0 + w0; x++) {
                had_true |= matrix[y][x];

                matrix[y][x] = !matrix[y][x];
                true_count += matrix[y][x] ? 1 : -1;
            }
        }

        return had_true;
    }

    bool all_false(const vector<vector<bool>> &matrix, int m, int n) {
        return true_count == 0;
    }

    int count_trues(const vector<vector<bool>> &matrix, int m, int n) {
        int count = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    count++;
                }
            }
        }
        return count;
    }

    bool extend_diff_matrix(int m, int n, int y0, int x0, int h0, int w0, vector<vector<bool>> &matrix, int n_cur_rects) {
        if (all_false(matrix, m, n)) {
            return true;
        }

        if (n_cur_rects >= cur_min) {
            return false;
        }

        int h = h0;
        int w = w0;
        int y = y0;
        int x = x0;

        if (h0 == 0 || w0 == 0) {
            h = m;
            w = n;
            y = 0;
            x = 0;
        }

        for (h = m; h >= 1; h--) {
            for (w = n; w >= 1; w--) {
                for (y = 0; y <= m - h; y++) {
                    for (x = 0; x <= n - w; x++) {
                        if (h == h0 && w == w0 && y == y0 && x == x0) {
                            continue;
                        }

                        int old_true_count = true_count;

                        bool had_true = flip(matrix, y, x, h, w);

                        bool found = false;
                        if (had_true && true_count < old_true_count) {
                            found = extend_diff_matrix(m, n, y, x, h, w, matrix, n_cur_rects + 1);
                        }

                        flip(matrix, y, x, h, w);

                        if (found) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }


    int solve(int m, int n, vector<vector<bool>> matrix) {
        true_count = count_trues(matrix, m, n);

        for (cur_min = 0; cur_min < true_count; cur_min++) {
            bool found = extend_diff_matrix(m, n, 0, 0, 0, 0, &matrix, 0);

            if (found) {
                return cur_min;
            }
        }
        return true_count;
    }
}