public class Main {
    public static void main(String[] args) {
        int a;
        
        a = new Solution().solve(0, 0, new boolean[][] {});
        System.out.println(a + " 0");
        
        a = new Solution().solve(1, 1, new boolean[][] {
            new boolean[] { false },
        });
        System.out.println(a + " 0");
        
        a = new Solution().solve(1, 1, new boolean[][] {
            new boolean[] { true },
        });
        System.out.println(a + " 1");
        
        a = new Solution().solve(2, 2, new boolean[][] {
            new boolean[] { false, true },
            new boolean[] { false, true },
        });
        System.out.println(a + " 1");
        
        a = new Solution().solve(3, 2, new boolean[][] {
            new boolean[] { false, true },
            new boolean[] { true, true },
            new boolean[] { false, true },
        });
        System.out.println(a + " 2");

        a = new Solution().solve(5, 2, new boolean[][] {
            new boolean[] { true, false },
            new boolean[] { false, true },
            new boolean[] { true, false },
            new boolean[] { false, true },
            new boolean[] { true, false },
        });
        System.out.println(a + " 3");

        a = new Solution().solve(5, 4, new boolean[][] {
            new boolean[] { true, false, true, false },
            new boolean[] { false, true, false, false },
            new boolean[] { true, false, true, true },
            new boolean[] { false, true, false, false },
            new boolean[] { true, false, true, false },
        });
        System.out.println(a + " 4");
        
        a = new Solution().solve(8, 5, new boolean[][] {
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, false, true, true },
            new boolean[] { false, true, false, true, true },
            new boolean[] { false, false, true, true, true },
        });
        System.out.println(a + " 3");
        
        a = new Solution().solve(6, 6, new boolean[][] {
            new boolean[] { true, false, false, false, false, true },
            new boolean[] { false, true, true, true, true, false },
            new boolean[] { false, true, false, false, true, false },
            new boolean[] { false, true, false, false, true, false },
            new boolean[] { false, true, true, true, true, true },
            new boolean[] { true, false, false, false, false, true },
        });
        System.out.println(a + " 5");
        
        a = new Solution().solve(6, 6, new boolean[][] {
            new boolean[] { true, false, false, false, false, true },
            new boolean[] { false, false, false, false, true, false },
            new boolean[] { false, false, false, true, false, false },
            new boolean[] { false, false, true, false, false, false },
            new boolean[] { false, true, false, false, false, false },
            new boolean[] { true, false, false, false, false, true },
        });
        System.out.println(a + " 8");
    }
}