public class PerformanceTest {
    public static void main(String[] args) {
        // Test with a smaller matrix first
        boolean[][] largeMatrix = new boolean[6][6];

        // Create a pattern that requires multiple rectangles
        for (int i = 0; i < 6; i++) {
            for (int j = 0; j < 6; j++) {
                if ((i + j) % 3 == 0) {
                    largeMatrix[i][j] = true;
                }
            }
        }

        System.out.println("Testing performance with 4x4 matrix:");
        long startTime = System.currentTimeMillis();
        
        int result = new Solution().solve(6, 6, largeMatrix);
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("Result: " + result);
        System.out.println("Time taken: " + (endTime - startTime) + " ms");
        
        // Test with a different pattern
        boolean[][] veryLargeMatrix = new boolean[3][5];
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 5; j++) {
                if ((i * j) % 3 == 1) {
                    veryLargeMatrix[i][j] = true;
                }
            }
        }

        System.out.println("\nTesting performance with 3x5 matrix:");
        startTime = System.currentTimeMillis();
        
        result = new Solution().solve(3, 5, veryLargeMatrix);
        
        endTime = System.currentTimeMillis();
        
        System.out.println("Result: " + result);
        System.out.println("Time taken: " + (endTime - startTime) + " ms");
    }
}
