import java.util.*;

class Solution4 {
    private int trueCount = 0;
    private int curMin = Integer.MAX_VALUE;

    private boolean flip(boolean[][] matrix, int y0, int x0, int h0, int w0) {
        boolean hadTrue = false;

        for (int y = y0; y < y0 + h0; y++) {
            for (int x = x0; x < x0 + w0; x++) {
                hadTrue |= matrix[y][x];

                matrix[y][x] = !matrix[y][x];
                trueCount += matrix[y][x] ? 1 : -1;
            }
        }

        return hadTrue;
    }

    private boolean allFalse(boolean[][] matrix, int m, int n) {
        return trueCount == 0;
    }

    private int countTrues(boolean[][] matrix, int m, int n) {
        int count = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    count++;
                }
            }
        }
        return count;
    }

    private int hashMatrix(boolean[][] matrix, int m, int n) {
        int hash = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                hash = hash * 31 + (matrix[y][x] ? 1 : 0);
            }
        }
        return hash;
    }

    private int solveSub(int m, int n, int y0, int x0, int h0, int w0, boolean[][] matrix, int nCurRects) {
        if (allFalse(matrix, m, n)) {
            return nCurRects;
        }

        if (nCurRects >= curMin - 1) {
            return curMin;
        }

        int h = h0;
        int w = w0;
        int y = y0;
        int x = x0;

        if (h0 == 0 || w0 == 0) {
            h = m;
            w = n;
            y = 0;
            x = 0;
        }

        for (h = m; h >= 1; h--) {
            for (w = n; w >= 1; w--) {
                for (y = 0; y <= m - h; y++) {
                    for (x = 0; x <= n - w; x++) {
                        if (h == h0 && w == w0 && y == y0 && x == x0) {
                            continue;
                        }

                        boolean hadTrue = flip(matrix, y, x, h, w);

                        int foundCount = curMin;
                        if (hadTrue) {
                            foundCount = solveSub(m, n, y, x, h, w, matrix, nCurRects + 1);
                        }

                        flip(matrix, y, x, h, w);

                        if (foundCount < curMin) {
                            curMin = foundCount;
                        }
                    }
                }
            }
        }

        return curMin;
    }

    public int solve(int m, int n, boolean[][] matrix) {
        trueCount = countTrues(matrix, m, n);
        curMin = trueCount;
        return solveSub(m, n, 0, 0, 0, 0, matrix, 0);
    }
}